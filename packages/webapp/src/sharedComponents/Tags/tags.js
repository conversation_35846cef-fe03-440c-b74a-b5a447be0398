import React from 'react';
import PropTypes from 'prop-types';
import { get } from 'lodash';
import styles from './tags.module.scss';

const Tags = ({
  tags,
  maxLength = 15,
  textColor = '#fff',
  backgroundColor = '#333',
  showNoTags = false,
}) => {
  if (!Array.isArray(tags) || tags.length === 0) {
    return showNoTags ? (
      <div className="d-flex flex-wrap" style={{ gap: '16px' }}>
        <div
          className={styles.badgePill}
          style={{ backgroundColor, color: textColor }}
        >
          No tags
        </div>
      </div>
    ) : null;
  }

  const tagsToShow = [];
  let remainingTags = tags.length;

  tags.forEach((tag) => {
    const textLength = tagsToShow.reduce(
      (acc, curr) => acc + curr.text.length,
      0,
    );
    if (textLength + tag.text.length <= maxLength && remainingTags > 1) {
      tagsToShow.push(tag);
      remainingTags--;
    } else if (remainingTags === 1) {
      tagsToShow.push(tag);
      remainingTags--;
    }
  });

  return (
    <div className="d-flex flex-wrap" style={{ gap: '16px' }}>
      {tagsToShow.map((tag, index) => (
        <div
          key={index}
          className={styles.badgePill}
          style={{ backgroundColor, color: textColor }}
        >
          {get(tag, 'text')}
        </div>
      ))}
      {remainingTags > 0 && (
        <div
          className={styles.badgePill}
          style={{ backgroundColor, color: textColor }}
        >
          +{remainingTags} more
        </div>
      )}
    </div>
  );
};

Tags.propTypes = {
  tags: PropTypes.array.isRequired,
  maxLength: PropTypes.number,
  textColor: PropTypes.string,
  backgroundColor: PropTypes.string,
  showNoTags: PropTypes.bool, // New prop type
};

export default Tags;
