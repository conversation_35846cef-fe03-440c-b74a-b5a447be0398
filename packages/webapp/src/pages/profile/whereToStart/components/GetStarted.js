import React, { useState } from 'react';
import Router from 'next/router';
import WhereToStart from './whereToStart';
import Button from 'sharedComponents/Button/button';
import mixpanelAnalytics from 'lib/mixpanel';
import style from '../../styles/Profile.module.scss';

const GetStarted = ({ isEnterprise }) => {
  const [currentStep, setCurrentStep] = useState('');

  const moveToNextStep = () => {
    if (currentStep === 'createProject') {
      Router.push('/project/create');
      mixpanelAnalytics('project_creation_clicked', {
        pageName: 'profile_where_to_start',
        redirectUrl: '/project/create',
      });
    } else if (currentStep === 'discoverProjects') {
      Router.push('/discovery');
      mixpanelAnalytics('project_discovery_clicked', {
        pageName: 'profile_where_to_start',
        redirectUrl: '/discovery',
      });
    } else if (currentStep === 'createCallOut') {
      Router.push('/profile/createCallout');
      mixpanelAnalytics('callout_creation_clicked', {
        pageName: 'profile_where_to_start',
        redirectUrl: '/profile/createCallout',
      });
    }
  };

  return (
    <div className="row justify-content-center mb-5 mt-5">
      <div className="col-12">
        <div className="p-md-5 where-to-start-container">
          <div className="row justify-content-center">
            <div className="col-12">
              <h2
                data-cy="headingText"
                className="text-center text-primary mb-3"
              >
                Where do you want to start?
              </h2>
              <p
                data-cy="subHeadingText"
                className="p2 text-center text-primary"
              >
                Choose your first activity on SMASH
              </p>
            </div>
          </div>
          <div
            className={`row justify-content-center mt-24 mt-lg-48 ${style.whereToStartContainer}`}
            style={
              isEnterprise
                ? { justifyContent: 'center' }
                : {
                    gap: '',
                  }
            }
          >
            <WhereToStart
              id="createProject"
              src="/assets/svg/brush.svg"
              title="CREATE A PROJECT"
              text="Build your first project in SMASH, then share and submit to Call Outs."
              className={`${currentStep !== 'createProject' && currentStep !== '' ? style.opacity : ''} 
            ${isEnterprise ? style.isEnterprise : ''}`.trim()}
              setCurrentStep={setCurrentStep}
            />
            <WhereToStart
              id="discoverProjects"
              src="/assets/svg/archive.svg"
              title="DISCOVER PROJECTS"
              text="Discover Projects and Creators on SMASH to collaborate with."
              className={`${currentStep !== 'discoverProjects' && currentStep !== '' ? style.opacity : ''} 
            ${isEnterprise ? style.isEnterprise : ''}`.trim()}
              setCurrentStep={setCurrentStep}
            />
            {!isEnterprise && (
              <WhereToStart
                id="createCallOut"
                src="/assets/svg/megaphone.svg"
                title="CREATE A CALL OUT"
                text="Receive submissions from the SMASH community to find your perfect Project."
                className={
                  currentStep !== 'createCallOut' &&
                  currentStep !== '' &&
                  style.opacity
                }
                setCurrentStep={setCurrentStep}
              />
            )}
          </div>
          <div className="row justify-content-center mt-24 mt-sm-48 mt-lg-48">
            <div className="col-12 col-lg-5 col-md-5">
              <Button
                btntype="submit"
                id="submitbutton"
                size="small"
                className="w-100"
                customClass={currentStep !== '' ? 'modalBtn' : '--disabled'}
                buttonValue="Continue"
                clickHandler={moveToNextStep}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GetStarted;
